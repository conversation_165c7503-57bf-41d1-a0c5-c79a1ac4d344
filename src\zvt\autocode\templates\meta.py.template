# -*- coding: utf-8 -*-
from sqlalchemy import Column, DateTime, Boolean
from sqlalchemy.orm import declarative_base

from zvt.contract import EntityMixin
from zvt.contract.register import register_schema, register_entity

${entity_class}MetaBase = declarative_base()

@register_entity(entity_type='${entity_type}')
class ${entity_class}(EntityMixin, ${entity_class}MetaBase):
    __tablename__ = '${entity_type}'
    # 上市日
    list_date = Column(DateTime)
    # 退市日
    end_date = Column(DateTime)


register_schema(providers=${providers}, db_name='${entity_type}_meta', schema_base=${entity_class}MetaBase)

