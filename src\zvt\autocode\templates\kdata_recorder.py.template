# -*- coding: utf-8 -*-

# -*- coding: utf-8 -*-

from zvt import IntervalLevel
from zvt.api.kdata import get_kdata_schema
from zvt.contract.recorder import FixedCycleDataRecorder

from ${project}.domain import ${entity_class}, ${entity_class}KdataCommon


class ${Provider}${entity_class}KdataRecorder(FixedCycleDataRecorder):
    entity_provider = '${provider}'
    entity_schema = ${entity_class}

    provider = '${provider}'

    # register the recorder to data_schema
    data_schema = ${entity_class}KdataCommon

    def __init__(self, entity_type='${entity_type}', exchanges=None, entity_ids=None, codes=None, day_data=False,
                 force_update=True, sleeping_time=10, entity_filters=None, real_time=False, fix_duplicate_way='ignore',
             start_timestamp=None, end_timestamp=None,  level=IntervalLevel.LEVEL_1DAY,
             kdata_use_begin_time=False, one_day_trading_minutes=24 * 60) -> None:
        level = IntervalLevel(level)
        self.data_schema = get_kdata_schema(entity_type=entity_type, level=level, adjust_type=None)

        super().__init__(entity_type, exchanges, entity_ids, codes, day_data, force_update, sleeping_time,
                         entity_filters, real_time, fix_duplicate_way, start_timestamp, end_timestamp, close_hour,
                         close_minute, level, kdata_use_begin_time, one_day_trading_minutes)

    def record(self, entity, start, end, size, timestamps):
        pass
