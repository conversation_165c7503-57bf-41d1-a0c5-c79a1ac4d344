# -*- coding: utf-8 -*-
# this file is generated by gen_kdata_schema function, dont't change it
from sqlalchemy.orm import declarative_base

from zvt.contract.register import register_schema
from zvt.domain.quotes import IndexusKdataCommon

KdataBase = declarative_base()


class Indexus1dKdata(KdataBase, IndexusKdataCommon):
    __tablename__ = "indexus_1d_kdata"


register_schema(providers=["em"], db_name="indexus_1d_kdata", schema_base=KdataBase, entity_type="indexus")


# the __all__ is generated
__all__ = ["Indexus1dKdata"]
